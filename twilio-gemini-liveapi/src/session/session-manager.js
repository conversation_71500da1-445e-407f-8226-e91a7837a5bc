import { Modality } from '../gemini/client.js';
import { AudioProcessor } from '../audio/audio-processor.js';

// Session Manager for Gemini connections with recovery
export class SessionManager {
    constructor(contextManager, geminiClient) {
        this.contextManager = contextManager;
        this.geminiClient = geminiClient;
        this.recoveryInProgress = new Set();
        this.audioProcessor = new AudioProcessor();
        this.sessionMetrics = new Map();
    }

    // Create new Gemini session
    async createGeminiSession(callSid, config, connectionData) {
        try {
            console.log(`🤖 [${callSid}] Creating Gemini session with model: ${config.model}, voice: ${config.voice}`);

            const geminiSession = await this.geminiClient.live.connect({
                model: config.model,
                callbacks: {
                    onopen: () => {
                        console.log(`✅ [${callSid}] Gemini session opened`);
                        connectionData.isSessionActive = true;
                        
                        // Initialize session metrics
                        this.sessionMetrics.set(callSid, {
                            startTime: Date.now(),
                            messagesReceived: 0,
                            messagesSent: 0,
                            recoveryCount: 0,
                            lastActivity: Date.now()
                        });
                        
                        // Save initial context
                        this.contextManager.saveSessionContext(callSid, {
                            ...config,
                            ...connectionData,
                            conversationLog: [],
                            fullTranscript: []
                        });

                        // Note: Initial message will be sent after session creation
                    },

                    onerror: (error) => {
                        console.error(`❌ [${callSid}] Gemini session error:`, error);
                        this.contextManager.markSessionInterrupted(callSid, 'session_error');

                        // Update metrics
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.recoveryCount++;
                        }

                        // Mark session as inactive but don't end it - let recovery manager handle it
                        connectionData.isSessionActive = false;
                        connectionData.geminiSessionError = error.message;

                        console.log(`🔄 [${callSid}] Gemini session error detected, session marked for recovery`);
                        // Recovery will be handled by the recovery manager's health checks or explicit recovery calls
                    },

                    onclose: () => {
                        console.log(`🔌 [${callSid}] Gemini session closed`);
                        connectionData.isSessionActive = false;

                        // Check if this is an unexpected close (connection still active)
                        const isUnexpectedClose = connectionData.twilioWs && connectionData.twilioWs.readyState === 1; // WebSocket.OPEN
                        const isLocalTestingActive = connectionData.localWs && connectionData.localWs.readyState === 1;

                        if (isUnexpectedClose || isLocalTestingActive) {
                            console.log(`⚠️ [${callSid}] Unexpected Gemini session close detected`);
                            this.contextManager.markSessionInterrupted(callSid, 'session_closed_unexpected');

                            // Session will be recovered by recovery manager's health checks or explicit recovery calls
                            console.log(`🔄 [${callSid}] Session marked for recovery due to unexpected close`);
                        } else {
                            console.log(`✅ [${callSid}] Gemini session closed normally (connection also closed)`);
                        }
                    },

                    onmessage: async (message) => {
                        await this.handleGeminiMessage(callSid, message, connectionData);
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192,
                responseMimeType: 'text/plain'
            });

            return geminiSession;

        } catch (error) {
            console.error(`❌ [${callSid}] Error creating Gemini session:`, error);
            return null;
        }
    }

    // Handle messages from Gemini
    async handleGeminiMessage(callSid, message, connectionData) {
        try {
            // Update metrics
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesReceived++;
                metrics.lastActivity = Date.now();
            }

            // Handle audio response
            if (message?.serverContent?.modelTurn?.parts?.[0]?.inlineData) {
                const audio = message.serverContent.modelTurn.parts[0].inlineData;

                if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                    if (connectionData.twilioWs && connectionData.twilioWs.readyState === 1) {
                        const convertedAudio = this.audioProcessor.convertPCMToUlaw(audio.data);
                        const audioDelta = {
                            event: 'media',
                            streamSid: connectionData.streamSid,
                            media: { payload: convertedAudio }
                        };
                        connectionData.twilioWs.send(JSON.stringify(audioDelta));
                        console.log(`🔊 [${callSid}] Sent audio to Twilio`);
                    }
                }
            }

            // Handle text responses
            const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
            if (text) {
                console.log(`💬 [${callSid}] AI response: ${text.substring(0, 100)}...`);
                
                // Add to conversation logs
                if (connectionData.conversationLog) {
                    connectionData.conversationLog.push({
                        role: 'assistant',
                        content: text,
                        timestamp: Date.now()
                    });
                }

                if (connectionData.fullTranscript) {
                    connectionData.fullTranscript.push({
                        role: 'assistant',
                        content: text,
                        timestamp: Date.now()
                    });
                }

                // Handle summary if requested
                if (connectionData.summaryRequested) {
                    connectionData.summaryText += text;
                }

                // Update context
                this.contextManager.saveSessionContext(callSid, connectionData);
            }

        } catch (error) {
            console.error(`❌ [${callSid}] Error handling Gemini message:`, error);
        }
    }

    // Send initial message to AI
    async sendInitialMessage(geminiSession, aiInstructions) {
        try {
            if (geminiSession && aiInstructions) {
                await geminiSession.sendClientContent({
                    turns: [{
                        role: 'user',
                        parts: [{
                            text: aiInstructions
                        }]
                    }],
                    turnComplete: true
                });
                console.log('📤 Initial AI instructions sent');
            }
        } catch (error) {
            console.error('❌ Error sending initial message:', error);
        }
    }

    // Send audio to Gemini session
    async sendAudioToGemini(callSid, geminiSession, audioBuffer) {
        try {
            if (!geminiSession || !audioBuffer) return;

            // Update metrics
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.messagesSent++;
                metrics.lastActivity = Date.now();
            }

            // Convert Twilio audio to Gemini format
            const pcmBuffer = this.audioProcessor.convertUlawToPCM(audioBuffer);
            const float32Data = this.audioProcessor.pcmToFloat32Array(pcmBuffer);
            const audioBlob = this.audioProcessor.createGeminiAudioBlob(float32Data);

            // Send to Gemini
            await geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        inlineData: audioBlob
                    }]
                }],
                turnComplete: false
            });

        } catch (error) {
            console.error(`❌ [${callSid}] Error sending audio to Gemini:`, error);
        }
    }

    // Recover session after interruption
    async recoverSession(callSid, reason) {
        if (this.recoveryInProgress.has(callSid)) {
            console.log(`⏳ [${callSid}] Recovery already in progress`);
            return;
        }

        this.recoveryInProgress.add(callSid);
        
        try {
            const context = this.contextManager.getSessionContext(callSid);
            if (!context || !this.contextManager.canRecover(callSid)) {
                console.log(`❌ [${callSid}] Cannot recover session`);
                return;
            }

            // Get connection data from active connections (would need to be passed in)
            // This is a simplified version - in practice you'd need access to activeConnections
            const recoveryCount = this.contextManager.incrementRecoveryAttempt(callSid);
            console.log(`🔄 [${callSid}] Attempting session recovery #${recoveryCount} (reason: ${reason})`);

            // Update metrics
            const metrics = this.sessionMetrics.get(callSid);
            if (metrics) {
                metrics.recoveryCount++;
            }

            // The actual recovery would happen in the main connection handler
            // This method primarily handles the recovery logic and context preparation
            
            console.log(`✅ [${callSid}] Recovery preparation completed`);

        } catch (error) {
            console.error(`❌ [${callSid}] Error during session recovery:`, error);
        } finally {
            this.recoveryInProgress.delete(callSid);
        }
    }

    // Generate session summary
    async generateSummary(callSid, connectionData, summaryPrompt) {
        try {
            console.log(`📋 [${callSid}] Generating call summary`);
            
            if (!connectionData.geminiSession) {
                console.warn(`⚠️ [${callSid}] No Gemini session for summary generation`);
                return;
            }

            connectionData.summaryRequested = true;
            connectionData.summaryText = '';

            // Send summary request
            await connectionData.geminiSession.sendClientContent({
                turns: [{
                    role: 'user',
                    parts: [{
                        text: summaryPrompt
                    }]
                }],
                turnComplete: true
            });

            // Summary will be collected in handleGeminiMessage
            return true;

        } catch (error) {
            console.error(`❌ [${callSid}] Error generating summary:`, error);
            return false;
        }
    }

    // Get session metrics
    getSessionMetrics(callSid) {
        return this.sessionMetrics.get(callSid) || null;
    }

    // Clean up session
    cleanupSession(callSid) {
        this.sessionMetrics.delete(callSid);
        this.recoveryInProgress.delete(callSid);
        console.log(`🧹 [${callSid}] Session manager cleanup completed`);
    }
}
