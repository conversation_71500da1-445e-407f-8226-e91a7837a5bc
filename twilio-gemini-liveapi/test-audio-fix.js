#!/usr/bin/env node

/**
 * Test script to verify the local audio session fix
 * This simulates the frontend WebSocket connection and tests the backend
 */

import WebSocket from 'ws';

console.log('🧪 Testing Twilio Gemini Local Audio Session Fix...\n');

// Test configuration
const testConfig = {
    voice: 'Orus',
    model: 'gemini-2.5-flash-preview-native-audio-dialog',
    language: 'cz',
    task: '{"campaign": "Test Campaign", "greeting": "Hello, this is a test call."}',
    targetName: 'Test User',
    targetPhoneNumber: '+420733154483'
};

// Connect to the WebSocket (try local first)
const ws = new WebSocket('ws://localhost:3101/local-audio-session');

let sessionStartTime = Date.now();
let configSent = false;
let testAudioSent = false;

ws.on('open', () => {
    console.log('✅ WebSocket connected successfully');
    
    // Send configuration
    console.log('📤 Sending test configuration...');
    ws.send(JSON.stringify({
        type: 'start-session',
        ...testConfig
    }));
    configSent = true;
    
    // Wait a bit, then send test audio data
    setTimeout(() => {
        if (!testAudioSent) {
            console.log('📤 Sending test audio data...');
            
            // Create dummy audio data (base64 encoded)
            const dummyAudioData = Buffer.from('test audio data').toString('base64');
            
            ws.send(JSON.stringify({
                type: 'audio-data',
                audioData: dummyAudioData
            }));
            testAudioSent = true;
        }
    }, 2000);
    
    // Close connection after test
    setTimeout(() => {
        console.log('🔚 Closing test connection...');
        ws.close();
    }, 5000);
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data.toString());
        console.log('📨 Received message:', {
            type: message.type,
            hasAudio: !!message.audio,
            audioSize: message.audio ? message.audio.length : 0,
            fullMessage: message
        });

        if (message.type === 'audio') {
            console.log('🎵 ✅ SUCCESS: Received audio response from Gemini!');
            console.log('🎉 The audio fix is working correctly!');
        } else if (message.type === 'error') {
            console.log('❌ Error from backend:', message.message);
        } else if (message.type === 'session-started') {
            console.log('✅ Session started successfully!');
        } else if (message.type === 'session-error') {
            console.log('❌ Session error:', message.error);
        }
    } catch (error) {
        console.log('📨 Received non-JSON message:', data.toString().substring(0, 100));
    }
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
});

ws.on('close', (code, reason) => {
    const duration = Date.now() - sessionStartTime;
    console.log(`🔌 WebSocket closed after ${duration}ms`);
    console.log(`   Code: ${code}, Reason: ${reason || 'No reason provided'}`);
    
    // Test results summary
    console.log('\n📊 Test Results Summary:');
    console.log(`✅ WebSocket Connection: SUCCESS`);
    console.log(`${configSent ? '✅' : '❌'} Configuration Sent: ${configSent ? 'SUCCESS' : 'FAILED'}`);
    console.log(`${testAudioSent ? '✅' : '❌'} Test Audio Sent: ${testAudioSent ? 'SUCCESS' : 'FAILED'}`);
    console.log(`⏱️  Session Duration: ${duration}ms`);
    
    if (duration > 4000) {
        console.log('✅ Session lasted long enough - no immediate crash detected');
        console.log('🎯 The backend fix appears to be working!');
    } else {
        console.log('⚠️  Session ended quickly - may indicate an issue');
    }
    
    process.exit(0);
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Test interrupted by user');
    ws.close();
    process.exit(0);
});

console.log('⏳ Connecting to WebSocket...');
